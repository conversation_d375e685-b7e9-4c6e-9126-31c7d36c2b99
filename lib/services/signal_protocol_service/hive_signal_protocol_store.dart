import 'dart:convert';

import 'package:flutter_audio_room/core/utils/log_utils.dart';
import 'package:hive_flutter/hive_flutter.dart';
import 'package:libsignal_protocol_dart/libsignal_protocol_dart.dart';

class HiveSignalProtocolStore implements SignalProtocolStore, SenderKeyStore {
  final Box box;
  final String userId;

  static const String _sessionPrefix = 'session_';
  static const String _preKeyPrefix = 'prekey_';
  static const String _signedPreKeyPrefix = 'signed_prekey_';
  static const String _identityKeyPrefix = 'identity_key_';
  static const String _trustedKeysPrefix = 'trusted_';
  static const String _senderKeyPrefix = 'sender_key_';

  HiveSignalProtocolStore(this.box, this.userId);

  // Session 存储
  @override
  Future<SessionRecord> loadSession(SignalProtocolAddress address) async {
    try {
      final key = _getSessionKey(address);
      final data = await box.get(key);
      if (data == null) {
        throw Exception('会话不存在');
      }
      return SessionRecord.fromSerialized(base64Decode(data));
    } catch (e) {
      LogUtils.e('加载会话失败: $e', tag: 'HiveSignalProtocolStore');
      rethrow;
    }
  }

  @override
  Future<List<int>> getSubDeviceSessions(String name) async {
    final sessions = <int>[];
    final prefix = '$_sessionPrefix$userId:$name:';

    for (final key in box.keys) {
      if (key.toString().startsWith(prefix)) {
        final deviceId = int.tryParse(key.toString().substring(prefix.length));
        if (deviceId != null) {
          sessions.add(deviceId);
        }
      }
    }

    return sessions;
  }

  @override
  Future<void> storeSession(
      SignalProtocolAddress address, SessionRecord record) async {
    try {
      final key = _getSessionKey(address);
      await box.put(key, base64Encode(record.serialize()));
    } catch (e) {
      LogUtils.e('保存会话失败: $e', tag: 'HiveSignalProtocolStore');
    }
  }

  @override
  Future<bool> containsSession(SignalProtocolAddress address) async {
    final key = _getSessionKey(address);
    if (!box.containsKey(key)) return false;

    final sessionRecord = await loadSession(address);
    return sessionRecord.sessionState.hasSenderChain() &&
        sessionRecord.sessionState.getSessionVersion() ==
            CiphertextMessage.currentVersion;
  }

  @override
  Future<void> deleteSession(SignalProtocolAddress address) async {
    final key = _getSessionKey(address);
    await box.delete(key);
  }

  @override
  Future<void> deleteAllSessions(String name) async {
    final prefix = '$_sessionPrefix$userId:$name:';
    final keys = box.keys.where((key) => key.toString().startsWith(prefix));
    for (final key in keys) {
      await box.delete(key);
    }
  }

  Future<List<PreKeyRecord>> loadPreKeys() async {
    final prefix = '$_preKeyPrefix$userId:';
    final keys = box.keys.where((key) => key.toString().startsWith(prefix));
    final records = <PreKeyRecord>[];
    for (final key in keys) {
      try {
        final data = await box.get(key);
        if (data != null) {
          records.add(PreKeyRecord.fromBuffer(base64Decode(data)));
        }
      } catch (e) {
        LogUtils.e('加载预密钥失败: $e', tag: 'HiveSignalProtocolStore');
      }
    }
    return records;
  }

  // PreKey 存储
  @override
  Future<PreKeyRecord> loadPreKey(int preKeyId) async {
    final key = _getPreKeyKey(preKeyId);
    final data = await box.get(key);
    if (data == null) {
      throw Exception('预密钥 $preKeyId 不存在');
    }
    return PreKeyRecord.fromBuffer(base64Decode(data));
  }

  @override
  Future<void> storePreKey(int preKeyId, PreKeyRecord record) async {
    final key = _getPreKeyKey(preKeyId);
    await box.put(key, base64Encode(record.serialize()));
  }

  @override
  Future<bool> containsPreKey(int preKeyId) async {
    final key = _getPreKeyKey(preKeyId);
    return box.containsKey(key);
  }

  @override
  Future<void> removePreKey(int preKeyId) async {
    final key = _getPreKeyKey(preKeyId);
    await box.delete(key);
  }

  // SignedPreKey 存储
  @override
  Future<SignedPreKeyRecord> loadSignedPreKey(int signedPreKeyId) async {
    final key = _getSignedPreKeyKey(signedPreKeyId);
    final data = await box.get(key);
    if (data == null) {
      throw Exception('签名预密钥不存在');
    }
    return SignedPreKeyRecord.fromSerialized(base64Decode(data));
  }

  @override
  Future<void> storeSignedPreKey(
    int signedPreKeyId,
    SignedPreKeyRecord record,
  ) async {
    final key = _getSignedPreKeyKey(signedPreKeyId);
    await box.put(key, base64Encode(record.serialize()));
  }

  @override
  Future<bool> containsSignedPreKey(int signedPreKeyId) async {
    final key = _getSignedPreKeyKey(signedPreKeyId);
    return box.containsKey(key);
  }

  @override
  Future<void> removeSignedPreKey(int signedPreKeyId) async {
    final key = _getSignedPreKeyKey(signedPreKeyId);
    await box.delete(key);
  }

  @override
  Future<List<SignedPreKeyRecord>> loadSignedPreKeys() async {
    final prefix = '$_signedPreKeyPrefix$userId:';
    final keys = box.keys.where((key) => key.toString().startsWith(prefix));
    final records = <SignedPreKeyRecord>[];

    for (final key in keys) {
      try {
        final data = await box.get(key);
        if (data != null) {
          records.add(SignedPreKeyRecord.fromSerialized(base64Decode(data)));
        }
      } catch (e) {
        LogUtils.e('加载签名预密钥失败: $e', tag: 'HiveSignalProtocolStore');
      }
    }

    return records;
  }

  // Identity Key 存储
  @override
  Future<IdentityKeyPair> getIdentityKeyPair() async {
    final key = _getIdentityKeyPairKey();
    final data = await box.get(key);
    if (data == null) {
      final identityKeyPair = generateIdentityKeyPair();
      await box.put(key, base64Encode(identityKeyPair.serialize()));
      return identityKeyPair;
    }
    return IdentityKeyPair.fromSerialized(base64Decode(data));
  }

  @override
  Future<int> getLocalRegistrationId() async {
    final key = _getLocalRegistrationIdKey();
    final data = await box.get(key);
    if (data == null) {
      final registrationId = generateRegistrationId(false);
      await box.put(key, registrationId);
      return registrationId;
    }
    return data;
  }

  @override
  Future<bool> saveIdentity(
      SignalProtocolAddress address, IdentityKey? identityKey) async {
    final key = _getIdentityKey(address);
    final existing = await box.get(key);

    if (identityKey == null) {
      await box.delete(key);
      return false;
    }

    await box.put(key, base64Encode(identityKey.serialize()));
    return existing != null &&
        existing != base64Encode(identityKey.serialize());
  }

  @override
  Future<bool> isTrustedIdentity(
    SignalProtocolAddress address,
    IdentityKey? identityKey,
    Direction direction,
  ) async {
    if (identityKey == null) {
      return false;
    }

    final key = _getTrustedKey(address);
    final trusted = await box.get(key);

    if (trusted == null) {
      return true; // 首次信任
    }

    return trusted == base64Encode(identityKey.serialize());
  }

  @override
  Future<IdentityKey?> getIdentity(SignalProtocolAddress address) async {
    final key = _getIdentityKey(address);
    final data = await box.get(key);
    if (data == null) {
      return null;
    }
    return IdentityKey.fromBytes(base64Decode(data), 0);
  }

  // 初始化方法
  Future<void> initialize(
      IdentityKeyPair identityKeyPair, int registrationId) async {
    await box.put(
        _getIdentityKeyPairKey(), base64Encode(identityKeyPair.serialize()));
    await box.put(_getLocalRegistrationIdKey(), registrationId);
  }

  // 辅助方法
  String _getSessionKey(SignalProtocolAddress address) {
    return '$_sessionPrefix$userId:${address.getName()}:${address.getDeviceId()}';
  }

  String _getPreKeyKey(int preKeyId) {
    return '$_preKeyPrefix$userId:$preKeyId';
  }

  String _getSignedPreKeyKey(int signedPreKeyId) {
    return '$_signedPreKeyPrefix$userId:$signedPreKeyId';
  }

  String _getIdentityKey(SignalProtocolAddress address) {
    return '$_identityKeyPrefix$userId:${address.getName()}';
  }

  String _getTrustedKey(SignalProtocolAddress address) {
    return '$_trustedKeysPrefix$userId:${address.getName()}';
  }

  String _getIdentityKeyPairKey() {
    return '${_identityKeyPrefix}pair_$userId';
  }

  String _getLocalRegistrationIdKey() {
    return '${_identityKeyPrefix}regid_$userId';
  }

  Future<int> getMaxPreKeyId() async {
    int maxId = 0;
    final prefix = '$_preKeyPrefix$userId:';

    for (final key in box.keys) {
      if (key.toString().startsWith(prefix)) {
        final preKeyId = int.tryParse(key.toString().substring(prefix.length));
        if (preKeyId != null && preKeyId > maxId) {
          maxId = preKeyId;
        }
      }
    }

    return maxId;
  }

  Future<Map<String, dynamic>> getPreKeyBundle() async {
    final maxPreKeyId = await getMaxPreKeyId();
    final signedPreKey = await loadSignedPreKey(maxPreKeyId);
    final identityKey = await getIdentityKeyPair();

    final preKeys = await loadPreKeys();
    final base64PreKeys = <String>[];

    for (var i = 0; i < preKeys.length; i++) {
      try {
        final preKey = preKeys[i];
        base64PreKeys.add(base64Encode(preKey.serialize()));
      } catch (e) {
        LogUtils.e('加载预密钥失败: $e', tag: 'HiveSignalProtocolStore');
      }
    }

    return {
      'registrationId': await getLocalRegistrationId(),
      'deviceId': 1,
      'identityKey': base64Encode(identityKey.getPublicKey().serialize()),
      'signedPreKey': base64Encode(signedPreKey.serialize()),
      'preKeys': base64PreKeys,
    };
  }

  // SenderKeyStore implementation
  @override
  Future<void> storeSenderKey(
    SenderKeyName senderKeyName,
    SenderKeyRecord record,
  ) async {
    try {
      final key = _getSenderKeyKey(senderKeyName);
      await box.put(key, base64Encode(record.serialize()));
      LogUtils.d('保存发送者密钥成功', tag: 'HiveSignalProtocolStore');
    } catch (e) {
      LogUtils.e('保存发送者密钥失败: $e', tag: 'HiveSignalProtocolStore');
      rethrow;
    }
  }

  @override
  Future<SenderKeyRecord> loadSenderKey(SenderKeyName senderKeyName) async {
    try {
      final key = _getSenderKeyKey(senderKeyName);
      final data = await box.get(key);
      if (data == null) {
        return SenderKeyRecord();
      }
      return SenderKeyRecord.fromSerialized(base64Decode(data));
    } catch (e) {
      LogUtils.e('加载发送者密钥失败: $e', tag: 'HiveSignalProtocolStore');
      return SenderKeyRecord();
    }
  }

  String _getSenderKeyKey(SenderKeyName senderKeyName) {
    return '$_senderKeyPrefix$userId:${senderKeyName.groupId}:${senderKeyName.sender.getName()}:${senderKeyName.sender.getDeviceId()}';
  }
}
